// Database
export { default as prisma } from './database/prisma';

// Types
export * from './types/api-response';

// Utilities
export * from './lib/api-error';
export * from './lib/utils';
export { apiClient } from './lib/api-client';

// Re-export Prisma types
export {
    // Export the PrismaClient class
    PrismaClient,
    // Export enums
    $Enums,
    Prisma,
    ParkingType,
    ReportType,
    UserRole,
    // Export model types
    type Event,
    type Floor,
    type Mall,
    type MallHour,
    type Offer,
    type PageView,
    type Report,
    type Shop,
    type ShopHour,
    type ShopOffer,
    type User,
} from './generated/prisma';
export * from './generated/zod';


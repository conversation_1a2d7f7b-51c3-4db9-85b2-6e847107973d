generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

generator zod {
  provider = "zod-prisma-types"
  output   = "../src/generated/zod"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String    @id @default(uuid())
  name        String
  email       String    @unique
  password    String
  phone       String?
  avatar      String?
  role        UserRole  @default(user)
  isActive    Boolean   @default(true)
  lastLoginAt DateTime?
  preferences Json?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  pageViews PageView[]
  reports   Report[]
  malls     Mall[]     // Malls managed by mall_manager
  shops     Shop[]     // Shops owned by shop_owner
  events    Event[]    // Events created by mall_manager
  offers    Offer[]    // Offers created by shop_owner

  @@index([email])
  @@index([role])
  @@index([isActive])
}

model PageView {
  id        String   @id @default(uuid())
  page      String
  title     String?
  referrer  String?
  userAgent String?
  ipAddress String?
  sessionId String?
  duration  Int?
  viewedAt  DateTime @default(now())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId String?
  user   User?   @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([page])
  @@index([viewedAt])
}

model Report {
  id          String     @id @default(uuid())
  title       String
  description String?
  type        ReportType
  data        Json
  filters     Json?
  dateFrom    DateTime
  dateTo      DateTime
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  generatedBy String
  user        User   @relation(fields: [generatedBy], references: [id])

  @@index([type])
  @@index([generatedBy])
}

model Mall {
  id               String       @id @default(uuid())
  name             String
  slug             String       @unique
  description      String?
  address          String?
  city             String?
  phone            String?
  email            String?
  images           String[]
  amenities        String[]
  logo             String?
  website          String?
  rating           Float?
  parkingAvailable Boolean      @default(false)
  parkingType      ParkingType? @default(free)
  isActive         Boolean      @default(true)
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt

  // Relations
  userId    String?    // Manager of the mall
  user      User?      @relation(fields: [userId], references: [id])
  mallHours MallHour[]
  events    Event[]
  floors    Floor[]
  shops     Shop[]
  offers    Offer[]

  @@index([name])
  @@index([city])
  @@index([isActive])
}

model MallHour {
  id        String   @id @default(uuid())
  day       Int      // 0-6 (Sun-Sat)
  open      String
  close     String
  isClosed  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  mallId String
  mall   Mall   @relation(fields: [mallId], references: [id])

  @@index([mallId])
}

model Event {
  id          String   @id @default(uuid())
  title       String
  description String
  date        DateTime
  endDate     DateTime
  image       String?
  location    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  mallId    String
  mall      Mall   @relation(fields: [mallId], references: [id])
  createdBy String
  user      User   @relation(fields: [createdBy], references: [id])

  @@index([mallId])
  @@index([date])
}

model Offer {
  id          String   @id @default(uuid())
  title       String
  description String
  validFrom   DateTime
  validTo     DateTime
  terms       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  mallId     String
  mall       Mall       @relation(fields: [mallId], references: [id])
  createdBy  String
  user       User       @relation(fields: [createdBy], references: [id])
  shopOffers ShopOffer[]

  @@index([mallId])
  @@index([validFrom])
  @@index([validTo])
}

model ShopOffer {
  shopId  String
  offerId String
  shop    Shop   @relation(fields: [shopId], references: [id])
  offer   Offer  @relation(fields: [offerId], references: [id])

  @@id([shopId, offerId])
  @@index([shopId])
  @@index([offerId])
}

model Floor {
  id         String   @id @default(uuid())
  number     Int
  name       String
  mapImage   String?
  categories String[] // e.g., ["Food", "Fashion"]
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  mallId String
  mall   Mall    @relation(fields: [mallId], references: [id])
  shops  Shop[]

  @@index([mallId])
  @@index([number])
}

model Shop {
  id          String    @id @default(uuid())
  name        String
  description String?
  category    String
  phone       String?
  images      String[]
  email       String?
  website     String?
  coordinates String?   // e.g., "x:10,y:20"
  logo        String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  mallId     String
  mall       Mall       @relation(fields: [mallId], references: [id])
  floorId    String?
  floor      Floor?     @relation(fields: [floorId], references: [id])
  userId     String?    // Shop owner
  user       User?      @relation(fields: [userId], references: [id])
  shopHours  ShopHour[]
  shopOffers ShopOffer[]

  @@index([mallId])
  @@index([floorId])
  @@index([name])
}

model ShopHour {
  id        String   @id @default(uuid())
  day       Int      // 0-6 (Sun-Sat)
  open      String
  close     String
  isClosed  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  shopId String
  shop   Shop   @relation(fields: [shopId], references: [id])

  @@index([shopId])
}

enum ParkingType {
  free
  paid
  valet
}

enum UserRole {
  admin       // Full system access
  mall_manager // Manages malls, events, and shop accounts
  shop_owner  // Manages shop data and offers
  user        // Views malls, shops, events, offers
}

enum ReportType {
  MALL_ANALYTICS
  SHOP_ANALYTICS
  USER_ANALYTICS
  PAGE_VIEW_ANALYTICS
}
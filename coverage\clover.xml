<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1750778169718" clover="3.2.0">
  <project timestamp="1750778169718" name="All files">
    <metrics statements="137" coveredstatements="104" conditionals="3985" coveredconditionals="242" methods="1382" coveredmethods="103" elements="5504" coveredelements="449" complexity="0" loc="137" ncloc="137" packages="3" files="5" classes="5"/>
    <package name="generated.prisma">
      <metrics statements="68" coveredstatements="63" conditionals="8" coveredconditionals="4" methods="1" coveredmethods="0"/>
      <file name="index.js" path="C:\Coding\Websites\mallsurf\packages_depecrated\core\src\generated\prisma\index.js">
        <metrics statements="68" coveredstatements="63" conditionals="8" coveredconditionals="4" methods="1" coveredmethods="0"/>
        <line num="5" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="235" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="256" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="265" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="281" count="1" type="stmt"/>
        <line num="288" count="1" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="311" count="1" type="stmt"/>
        <line num="361" count="1" type="stmt"/>
        <line num="363" count="1" type="stmt"/>
        <line num="364" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="365" count="0" type="stmt"/>
        <line num="370" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="371" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="378" count="1" type="stmt"/>
        <line num="379" count="1" type="stmt"/>
        <line num="380" count="1" type="stmt"/>
        <line num="381" count="1" type="stmt"/>
        <line num="384" count="1" type="stmt"/>
        <line num="386" count="1" type="stmt"/>
        <line num="391" count="1" type="stmt"/>
        <line num="392" count="1" type="stmt"/>
        <line num="393" count="1" type="stmt"/>
        <line num="396" count="1" type="stmt"/>
        <line num="397" count="1" type="stmt"/>
        <line num="399" count="1" type="stmt"/>
        <line num="400" count="1" type="stmt"/>
      </file>
    </package>
    <package name="generated.prisma.runtime">
      <metrics statements="50" coveredstatements="26" conditionals="3971" coveredconditionals="234" methods="1378" coveredmethods="101"/>
      <file name="library.js" path="C:\Coding\Websites\mallsurf\packages_depecrated\core\src\generated\prisma\runtime\library.js">
        <metrics statements="50" coveredstatements="26" conditionals="3971" coveredconditionals="234" methods="1378" coveredmethods="101"/>
        <line num="4" count="979" type="cond" truecount="71" falsecount="105"/>
        <line num="5" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="6" count="26" type="cond" truecount="29" falsecount="161"/>
        <line num="7" count="8" type="cond" truecount="0" falsecount="190"/>
        <line num="8" count="1" type="cond" truecount="0" falsecount="39"/>
        <line num="9" count="1" type="cond" truecount="29" falsecount="150"/>
        <line num="12" count="1" type="cond" truecount="0" falsecount="10"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="15" count="1" type="cond" truecount="8" falsecount="24"/>
        <line num="16" count="1" type="cond" truecount="2" falsecount="12"/>
        <line num="18" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="24" count="292" type="cond" truecount="89" falsecount="1266"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="1" type="cond" truecount="0" falsecount="40"/>
        <line num="29" count="1" type="cond" truecount="0" falsecount="39"/>
        <line num="30" count="1" type="cond" truecount="0" falsecount="75"/>
        <line num="31" count="3" type="cond" truecount="1" falsecount="135"/>
        <line num="32" count="1" type="cond" truecount="2" falsecount="300"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="1" type="cond" truecount="0" falsecount="267"/>
        <line num="36" count="1" type="cond" truecount="0" falsecount="14"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="67" count="1" type="cond" truecount="0" falsecount="31"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="1" type="cond" truecount="0" falsecount="7"/>
        <line num="72" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="113" count="1" type="cond" truecount="0" falsecount="109"/>
        <line num="114" count="1" type="cond" truecount="0" falsecount="184"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="50"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="47"/>
        <line num="117" count="1" type="cond" truecount="0" falsecount="51"/>
        <line num="124" count="1" type="cond" truecount="0" falsecount="147"/>
        <line num="125" count="1" type="cond" truecount="0" falsecount="14"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="16"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="78"/>
        <line num="133" count="1" type="cond" truecount="2" falsecount="137"/>
        <line num="134" count="1" type="cond" truecount="1" falsecount="11"/>
      </file>
    </package>
    <package name="lib">
      <metrics statements="19" coveredstatements="15" conditionals="6" coveredconditionals="4" methods="3" coveredmethods="2"/>
      <file name="api-client.ts" path="C:\Coding\Websites\mallsurf\packages_depecrated\core\src\lib\api-client.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
      </file>
      <file name="api-error.ts" path="C:\Coding\Websites\mallsurf\packages_depecrated\core\src\lib\api-error.ts">
        <metrics statements="13" coveredstatements="9" conditionals="6" coveredconditionals="4" methods="2" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="13" count="3" type="stmt"/>
        <line num="14" count="3" type="stmt"/>
        <line num="15" count="3" type="stmt"/>
        <line num="16" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="17" count="1" type="stmt"/>
        <line num="19" count="2" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
      </file>
      <file name="utils.ts" path="C:\Coding\Websites\mallsurf\packages_depecrated\core\src\lib\utils.ts">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="4" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>

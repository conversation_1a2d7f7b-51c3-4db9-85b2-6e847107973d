import { cn } from '../../src/lib/utils';

test('cn merges class names', () => {
  expect(cn('px-2 py-1', 'bg-blue-500')).toBe('px-2 py-1 bg-blue-500');
});

test('cn handles conditional classes', () => {
  expect(cn('base', true && 'show', false && 'hide')).toBe('base show');
});

test('cn merges conflicting Tailwind classes', () => {
  expect(cn('px-2 px-4')).toBe('px-4');
});

test('cn handles empty input', () => {
  expect(cn()).toBe('');
});

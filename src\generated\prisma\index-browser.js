
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.1
 * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
 */
Prisma.prismaVersion = {
  client: "6.10.1",
  engine: "9b628578b3b7cae625e8c927178f15a170e74a9c"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  phone: 'phone',
  avatar: 'avatar',
  role: 'role',
  isActive: 'isActive',
  lastLoginAt: 'lastLoginAt',
  preferences: 'preferences',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PageViewScalarFieldEnum = {
  id: 'id',
  page: 'page',
  title: 'title',
  referrer: 'referrer',
  userAgent: 'userAgent',
  ipAddress: 'ipAddress',
  sessionId: 'sessionId',
  duration: 'duration',
  viewedAt: 'viewedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.ReportScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  type: 'type',
  data: 'data',
  filters: 'filters',
  dateFrom: 'dateFrom',
  dateTo: 'dateTo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  generatedBy: 'generatedBy'
};

exports.Prisma.MallScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  description: 'description',
  address: 'address',
  city: 'city',
  phone: 'phone',
  email: 'email',
  images: 'images',
  amenities: 'amenities',
  logo: 'logo',
  website: 'website',
  rating: 'rating',
  parkingAvailable: 'parkingAvailable',
  parkingType: 'parkingType',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.MallHourScalarFieldEnum = {
  id: 'id',
  day: 'day',
  open: 'open',
  close: 'close',
  isClosed: 'isClosed',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  mallId: 'mallId'
};

exports.Prisma.EventScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  date: 'date',
  endDate: 'endDate',
  image: 'image',
  location: 'location',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  mallId: 'mallId',
  createdBy: 'createdBy'
};

exports.Prisma.OfferScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  validFrom: 'validFrom',
  validTo: 'validTo',
  terms: 'terms',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  mallId: 'mallId',
  createdBy: 'createdBy'
};

exports.Prisma.ShopOfferScalarFieldEnum = {
  shopId: 'shopId',
  offerId: 'offerId'
};

exports.Prisma.FloorScalarFieldEnum = {
  id: 'id',
  number: 'number',
  name: 'name',
  mapImage: 'mapImage',
  categories: 'categories',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  mallId: 'mallId'
};

exports.Prisma.ShopScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  category: 'category',
  phone: 'phone',
  images: 'images',
  email: 'email',
  website: 'website',
  coordinates: 'coordinates',
  logo: 'logo',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  mallId: 'mallId',
  floorId: 'floorId',
  userId: 'userId'
};

exports.Prisma.ShopHourScalarFieldEnum = {
  id: 'id',
  day: 'day',
  open: 'open',
  close: 'close',
  isClosed: 'isClosed',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  shopId: 'shopId'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.UserRole = exports.$Enums.UserRole = {
  admin: 'admin',
  mall_manager: 'mall_manager',
  shop_owner: 'shop_owner',
  user: 'user'
};

exports.ReportType = exports.$Enums.ReportType = {
  MALL_ANALYTICS: 'MALL_ANALYTICS',
  SHOP_ANALYTICS: 'SHOP_ANALYTICS',
  USER_ANALYTICS: 'USER_ANALYTICS',
  PAGE_VIEW_ANALYTICS: 'PAGE_VIEW_ANALYTICS'
};

exports.ParkingType = exports.$Enums.ParkingType = {
  free: 'free',
  paid: 'paid',
  valet: 'valet'
};

exports.Prisma.ModelName = {
  User: 'User',
  PageView: 'PageView',
  Report: 'Report',
  Mall: 'Mall',
  MallHour: 'MallHour',
  Event: 'Event',
  Offer: 'Offer',
  ShopOffer: 'ShopOffer',
  Floor: 'Floor',
  Shop: 'Shop',
  ShopHour: 'ShopHour'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)

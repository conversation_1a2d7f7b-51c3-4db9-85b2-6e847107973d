const fs = require('fs');
const path = require('path');

function copyDir(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      copyDir(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

const srcDir = path.resolve(__dirname, '../src/generated/prisma');
const destDir = path.resolve(__dirname, '../dist/generated/prisma');

console.log('📦 Copying Prisma generated files...');
console.log(`   From: ${srcDir}`);
console.log(`   To:   ${destDir}`);

try {
  if (!fs.existsSync(srcDir)) {
    console.warn('⚠️  Prisma source folder does not exist. Skipping copy.');
  } else {
    copyDir(srcDir, destDir);
    console.log('✅ Prisma files copied successfully!');
  }
} catch (error) {
  console.error('❌ Error copying Prisma files:', error);
  process.exit(1);
}

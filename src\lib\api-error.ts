import { NextResponse } from "next/server";

export class ApiError extends <PERSON>rror {
    statusCode: number;
    isOperational: boolean;

    constructor(
        statusCode: number,
        message: string,
        isOperational = true,
        stack = ''
    ) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        if (stack) {
            this.stack = stack;
        } else {
            Error.captureStackTrace(this, this.constructor);
        }
    }
}

export const errorHandler = (err: unknown) => {
    if (err instanceof ApiError) {
        return NextResponse.json(
            { success: false, error: err.message },
            { status: err.statusCode }
        );
    }
    console.error('Unexpected error:', err);
    return NextResponse.json(
        { success: false, error: 'Internal Server Error' },
        { status: 500 }
    )
}

import { apiClient } from '../../src/lib/api-client';

test('apiClient is defined and has HTTP methods', () => {
  expect(apiClient).toBeDefined();
  expect(typeof apiClient.get).toBe('function');
  expect(typeof apiClient.post).toBe('function');
  expect(typeof apiClient.put).toBe('function');
  expect(typeof apiClient.delete).toBe('function');
});

test('apiClient has correct default configuration', () => {
  expect(apiClient.defaults.baseURL).toBe('/api/v1');
  expect(apiClient.defaults.timeout).toBe(10000);
});

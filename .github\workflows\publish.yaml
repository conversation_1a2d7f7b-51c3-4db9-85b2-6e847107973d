name: Publish Package

on:
  push:
    branches:
      - main

jobs:
  publish-gpr:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - uses: actions/checkout@v4

      - uses: pnpm/action-setup@v3
        with:
          version: 8

      - uses: actions/setup-node@v4
        with: 
          node-version: 20
          registry-url: https://npm.pkg.github.com/
          scope: '@mallsurf-packages'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Run tests
        run: pnpm test
        if: always() # Optional: Remove if no tests are defined

      - name: Build package
        run: pnpm run build

      - name: Publish package
        run: pnpm publish --no-git-checks
        env: 
          NODE_AUTH_TOKEN: ${{secrets.GITHUB_TOKEN}}
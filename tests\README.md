# Tests

This directory contains tests for the mallsurf-core package.

## Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci
```

## Test Structure

- `tests/lib/` - Tests for utility functions and API helpers
- `tests/types/` - Tests for TypeScript type definitions
- `tests/enums.test.ts` - Tests for Prisma-generated enums

## Test Files

- **utils.test.ts** - Tests the `cn` utility function for class name merging
- **api-error.test.ts** - Tests the `ApiError` class and error handling
- **api-client.test.ts** - Tests the axios-based API client configuration
- **api-response.test.ts** - Tests TypeScript interfaces for API responses
- **enums.test.ts** - Tests Prisma-generated enums (UserRole, ParkingType, ReportType)

## Coverage

The tests provide good coverage for the core utility functions and types. Generated Prisma code is excluded from coverage requirements.

Current coverage focuses on:
- ✅ Utility functions (`cn`)
- ✅ Error handling (`ApiError`)
- ✅ API client configuration
- ✅ Type definitions
- ✅ Enum values

## Adding New Tests

When adding new functionality to the core package:

1. Create a test file in the appropriate directory
2. Use simple `test()` functions instead of complex `describe()` blocks
3. Focus on testing the actual functionality, not implementation details
4. Keep tests simple and readable

Example:
```typescript
import { myFunction } from '../src/lib/my-function';

test('myFunction does what it should', () => {
  expect(myFunction('input')).toBe('expected output');
});
```

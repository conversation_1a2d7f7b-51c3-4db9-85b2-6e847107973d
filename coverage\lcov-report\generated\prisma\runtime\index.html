
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for generated/prisma/runtime</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> generated/prisma/runtime</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">16.48% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>759/4603</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">5.89% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>234/3971</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.32% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>101/1378</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">52% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>26/50</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="library.js"><a href="library.js.html">library.js</a></td>
	<td data-value="16.48" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 16%"></div><div class="cover-empty" style="width: 84%"></div></div>
	</td>
	<td data-value="16.48" class="pct low">16.48%</td>
	<td data-value="4603" class="abs low">759/4603</td>
	<td data-value="5.89" class="pct low">5.89%</td>
	<td data-value="3971" class="abs low">234/3971</td>
	<td data-value="7.32" class="pct low">7.32%</td>
	<td data-value="1378" class="abs low">101/1378</td>
	<td data-value="52" class="pct medium">52%</td>
	<td data-value="50" class="abs medium">26/50</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-24T15:16:09.370Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    